-- Script SQL pour vider la table BOOKS
-- ATTENTION: Cette action est irréversible !
-- 
-- Usage avec SQLite CLI:
-- sqlite3 nookli_dev.db < scripts/clear-books-table.sql
--
-- Ou directement dans SQLite:
-- .read scripts/clear-books-table.sql

-- Afficher le nombre de livres avant suppression
.print "=== SUPPRESSION DE TOUS LES LIVRES ==="
.print "Nombre de livres avant suppression:"
SELECT COUNT(*) as total_books FROM BOOKS;

-- Supprimer tous les livres
DELETE FROM BOOKS;

-- Réinitialiser l'auto-increment (optionnel)
DELETE FROM sqlite_sequence WHERE name='BOOKS';

-- Afficher le nombre de livres après suppression
.print "Nombre de livres après suppression:"
SELECT COUNT(*) as total_books FROM BOOKS;

.print "=== SUPPRESSION TERMINÉE ==="
