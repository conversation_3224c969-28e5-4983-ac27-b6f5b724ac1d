<%- include('partials/header') %>

<!-- Messages Flash -->
<div class="container" style="margin-top: 1rem;">
    <%- include('partials/flash-messages') %>
</div>

<!-- Section Hero avec barre de recherche -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1><PERSON><PERSON><PERSON>, su<PERSON><PERSON>, découvrez</h1>
            <p class="hero-subtitle">Tous vos livres et histoires au même endroit.</p>

            <!-- Barre de recherche principale -->
            <div class="hero-search">
                <form action="/search" method="GET" class="search-form-hero">
                    <input type="search" name="q" placeholder="Rechercher un livre, un auteur...">
                    <button type="submit">Rechercher</button>
                </form>
            </div>

            <!-- Appels à l'action -->
            <div class="hero-cta">
                <% if (!currentUser) { %>
                    <a href="/register" class="btn btn-primary">S'inscrire gratuitement</a>
                    <a href="/login" class="btn btn-secondary">Se connecter</a>
                <% } else { %>
                    <a href="/dashboard" class="btn btn-primary">Mon espace lecture</a>
                    <a href="/books" class="btn btn-secondary">Explorer le catalogue</a>
                <% } %>
            </div>
        </div>
    </div>
</section>

<!-- Section Fonctionnalités -->
<section class="features">
    <div class="container">
        <h2>Pourquoi utiliser Nookli ?</h2>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📚</div>
                <h3>Gérez votre bibliothèque</h3>
                <p>Cataloguez et organisez vos livres lus, en cours de lecture ou à lire.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⭐</div>
                <h3>Partagez vos avis</h3>
                <p>Notez les livres et partagez vos impressions avec la communauté.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3>Découvrez de nouveaux titres</h3>
                <p>Explorez notre catalogue et trouvez votre prochaine lecture.</p>
            </div>
        </div>
    </div>
</section>

<!-- Section Livres Récents -->
<section class="book-showcase">
    <div class="container">
        <h2>Ajouts récents</h2>
        <p class="section-subtitle">Découvrez les derniers livres ajoutés à notre catalogue</p>

        <div class="books-grid">
            <% if (!recentBooks || recentBooks.length === 0) { %>
                <div class="empty-state">
                    <p>Aucun livre n'a encore été ajouté au catalogue.</p>
                    <% if (currentUser && currentUser.role === 'admin') { %>
                        <a href="/admin/books/new" class="btn btn-primary">Ajouter un livre</a>
                    <% } %>
                </div>
            <% } else { %>
                <% recentBooks.forEach(book => { %>
                    <div class="book-card">
                        <a href="/books/<%= book.id %>" class="book-link">
                            <div class="book-cover">
                                <img src="<%= book.cover_url || book.cover_image_url || '/images/placeholder-cover.svg' %>" alt="Couverture de <%= book.title %>">
                                <span class="book-badge">Nouveau</span>
                            </div>
                            <div class="book-info">
                                <h3><%= book.title %></h3>
                                <p class="book-author"><%= book.author %></p>
                                <div class="book-rating">
                                    <% if (book.average_rating > 0) { %>
                                        <% for (let i = 1; i <= 5; i++) { %>
                                            <% if (i <= Math.round(book.average_rating)) { %>
                                                <span class="star filled">★</span>
                                            <% } else { %>
                                                <span class="star">☆</span>
                                            <% } %>
                                        <% } %>
                                    <% } else { %>
                                        <span class="no-rating">Pas encore noté</span>
                                    <% } %>
                                </div>
                            </div>
                        </a>
                    </div>
                <% }); %>
            <% } %>
        </div>

        <div class="section-cta">
            <a href="/books" class="btn btn-primary">Voir tous les livres</a>
        </div>
    </div>
</section>

<!-- Section Livres les Mieux Notés -->
<section class="book-showcase">
    <div class="container">
        <h2>Les mieux notés</h2>
        <p class="section-subtitle">Les livres les plus appréciés par notre communauté</p>

        <div class="books-grid">
            <% if (!topRatedBooks || topRatedBooks.length === 0) { %>
                <div class="empty-state">
                    <p>Aucun livre noté n'est disponible pour le moment.</p>
                </div>
            <% } else { %>
                <% topRatedBooks.forEach(book => { %>
                    <div class="book-card">
                        <a href="/books/<%= book.id %>" class="book-link">
                            <div class="book-cover">
                                <img src="<%= book.cover_url || book.cover_image_url || '/images/placeholder-cover.svg' %>" alt="Couverture de <%= book.title %>">
                                <% if (book.average_rating >= 4.5) { %>
                                    <span class="book-badge">Top</span>
                                <% } %>
                            </div>
                            <div class="book-info">
                                <h3><%= book.title %></h3>
                                <p class="book-author"><%= book.author %></p>
                                <div class="book-rating">
                                    <% if (book.average_rating > 0) { %>
                                        <% for (let i = 1; i <= 5; i++) { %>
                                            <% if (i <= Math.round(book.average_rating)) { %>
                                                <span class="star filled">★</span>
                                            <% } else { %>
                                                <span class="star">☆</span>
                                            <% } %>
                                        <% } %>
                                    <% } else { %>
                                        <span class="no-rating">Pas encore noté</span>
                                    <% } %>
                                </div>
                            </div>
                        </a>
                    </div>
                <% }); %>
            <% } %>
        </div>

        <div class="section-cta">
            <a href="/books?sort=average_rating&order=DESC" class="btn btn-primary">Voir tous les livres bien notés</a>
        </div>
    </div>
</section>

<%- include('partials/footer') %>
