/**
 * Middleware pour vérifier si l'utilisateur est authentifié
 * Redirige vers la page de connexion si non authentifié
 */
export function isAuthenticated(req, res, next) {
    if (req.isAuthenticated()) {
        return next();
    }

    // Stocker l'URL d'origine pour rediriger après connexion
    req.session.returnTo = req.originalUrl;

    // Message flash pour informer l'utilisateur
    req.flash('error', 'Vous devez être connecté pour accéder à cette page');

    // Rediriger vers la page de connexion
    res.redirect('/login');
}

/**
 * Middleware pour vérifier si l'utilisateur est un administrateur
 * Renvoie une erreur 403 si l'utilisateur n'est pas admin
 */
export function isAdmin(req, res, next) {
    if (req.isAuthenticated() && req.user.role === 'admin') {
        return next();
    }

    // Message flash pour informer l'utilisateur
    req.flash('error', 'Accès refusé. Vous devez être administrateur pour accéder à cette page');

    // Rediriger vers la page d'accueil avec un statut 403
    res.status(403).redirect('/');
}

/**
 * Middleware pour vérifier si l'utilisateur n'est PAS authentifié
 * Utile pour les pages de connexion/inscription
 * Redirige vers le tableau de bord ou l'admin selon le rôle de l'utilisateur
 */
export function isNotAuthenticated(req, res, next) {
    if (!req.isAuthenticated()) {
        return next();
    }

    // Rediriger selon le rôle de l'utilisateur
    if (req.user.role === 'admin') {
        // Rediriger les administrateurs vers l'interface d'administration
        return res.redirect('/admin');
    } else {
        // Rediriger les lecteurs vers leur tableau de bord
        return res.redirect('/dashboard');
    }
}
