#!/bin/bash

# Script pour exécuter le script SQL de suppression des livres
# Usage: ./scripts/clear-books-sql.sh

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${RED}🚨 ATTENTION: SUPPRESSION DE TOUS LES LIVRES 🚨${NC}"
echo "=================================================="
echo -e "${YELLOW}⚠️  Cette action est IRRÉVERSIBLE !${NC}"
echo -e "${YELLOW}⚠️  Tous les livres seront supprimés de la base de données.${NC}"
echo "=================================================="
echo ""

# Vérifier si la base de données existe
if [ ! -f "nookli_dev.db" ]; then
    echo -e "${RED}❌ Erreur: Base de données 'nookli_dev.db' non trouvée.${NC}"
    echo "Assurez-vous d'être dans le répertoire racine du projet."
    exit 1
fi

# Demander confirmation
read -p "Voulez-vous vraiment continuer ? (tapez 'OUI' pour confirmer): " confirmation

if [ "$confirmation" != "OUI" ]; then
    echo -e "${YELLOW}❌ Opération annulée.${NC}"
    exit 0
fi

echo ""
echo -e "${GREEN}🔄 Exécution du script SQL...${NC}"

# Exécuter le script SQL
sqlite3 nookli_dev.db < scripts/clear-books-table.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Script exécuté avec succès !${NC}"
else
    echo -e "${RED}❌ Erreur lors de l'exécution du script.${NC}"
    exit 1
fi
