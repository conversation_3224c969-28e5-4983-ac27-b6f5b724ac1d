#!/usr/bin/env node

/**
 * Script pour vider la table BOOKS de la base de données locale
 * Usage: node scripts/clear-books-table.js [--confirm]
 * 
 * ATTENTION: Cette action est irréversible !
 * Utilise --confirm pour exécuter sans demander de confirmation
 */

import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { sequelize, Book } from '../backend/models/index.js';
import readline from 'readline';

// Configuration de l'environnement
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Charger les variables d'environnement
dotenv.config({ path: path.join(rootDir, '.env') });

// Interface readline pour les confirmations
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

/**
 * Affiche l'aide du script
 */
function showHelp() {
    console.log(`
Usage: node scripts/clear-books-table.js [options]

Options:
  --confirm     Exécute le script sans demander de confirmation
  --help        Affiche cette aide

ATTENTION: Cette action supprime TOUS les livres de la base de données !
Cette action est IRRÉVERSIBLE !

Exemples:
  node scripts/clear-books-table.js
  node scripts/clear-books-table.js --confirm
    `);
}

/**
 * Parse les arguments de la ligne de commande
 */
function parseArguments() {
    const args = process.argv.slice(2);
    
    if (args.includes('--help')) {
        showHelp();
        process.exit(0);
    }
    
    return {
        confirm: args.includes('--confirm')
    };
}

/**
 * Demande confirmation à l'utilisateur
 */
async function askConfirmation() {
    return new Promise((resolve) => {
        rl.question('⚠️  ATTENTION: Voulez-vous vraiment supprimer TOUS les livres ? (tapez "OUI" pour confirmer): ', (answer) => {
            resolve(answer.trim().toUpperCase() === 'OUI');
        });
    });
}

/**
 * Vide la table BOOKS
 */
async function clearBooksTable() {
    try {
        console.log('🔄 Connexion à la base de données...');
        
        // Vérifier la connexion
        await sequelize.authenticate();
        console.log('✅ Connexion établie avec succès.');
        
        // Compter les livres avant suppression
        const countBefore = await Book.count();
        console.log(`📊 Nombre de livres actuellement en base: ${countBefore}`);
        
        if (countBefore === 0) {
            console.log('ℹ️  La table BOOKS est déjà vide.');
            return;
        }
        
        console.log('🗑️  Suppression de tous les livres...');
        
        // Supprimer tous les livres
        const deletedCount = await Book.destroy({
            where: {},
            truncate: true // Plus efficace pour vider complètement la table
        });
        
        console.log(`✅ ${deletedCount} livres supprimés avec succès.`);
        
        // Vérifier que la table est bien vide
        const countAfter = await Book.count();
        console.log(`📊 Nombre de livres après suppression: ${countAfter}`);
        
        if (countAfter === 0) {
            console.log('🎉 La table BOOKS a été vidée avec succès !');
        } else {
            console.log('⚠️  Attention: Il reste encore des livres dans la table.');
        }
        
    } catch (error) {
        console.error('❌ Erreur lors de la suppression des livres:', error.message);
        throw error;
    }
}

/**
 * Fonction principale
 */
async function main() {
    try {
        const config = parseArguments();
        
        console.log('🚨 SCRIPT DE SUPPRESSION DE TOUS LES LIVRES 🚨');
        console.log('='.repeat(50));
        console.log('⚠️  ATTENTION: Cette action est IRRÉVERSIBLE !');
        console.log('⚠️  Tous les livres seront supprimés de la base de données.');
        console.log('='.repeat(50));
        console.log('');
        
        // Demander confirmation si pas de flag --confirm
        if (!config.confirm) {
            const confirmed = await askConfirmation();
            if (!confirmed) {
                console.log('❌ Opération annulée par l\'utilisateur.');
                process.exit(0);
            }
        } else {
            console.log('⚡ Mode confirmation automatique activé.');
        }
        
        console.log('');
        
        // Exécuter la suppression
        await clearBooksTable();
        
    } catch (error) {
        console.error(`❌ Erreur: ${error.message}`);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        rl.close();
        // Fermer la connexion Sequelize proprement
        try {
            await sequelize.close();
            console.log('🔌 Connexion à la base de données fermée.');
        } catch (error) {
            console.error('⚠️  Erreur lors de la fermeture de la connexion:', error);
        }
    }
}

// Exécuter le script
main();
